{"name": "fixing_issues", "description": "fixing_issues", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 60, "y": -40}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"agent-d1e8a246-a56c-40ce-9108-075da526d576-1751549184787_input": {"node_id": "agent-d1e8a246-a56c-40ce-9108-075da526d576-1751549184787", "node_name": "<PERSON><PERSON><PERSON>", "input_name": "input", "connected_to_start": true, "required": true, "input_type": "string"}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "agent-d1e8a246-a56c-40ce-9108-075da526d576-1751549184787", "type": "WorkflowNode", "position": {"x": 560, "y": -100}, "data": {"label": "<PERSON><PERSON><PERSON>", "type": "agent", "originalType": "agent-d1e8a246-a56c-40ce-9108-075da526d576", "definition": {"name": "agent-d1e8a246-a56c-40ce-9108-075da526d576", "display_name": "<PERSON><PERSON><PERSON>", "description": "Ciny is an AI-powered video generation assistant that helps users create high-quality video content with ease. It provides expert guidance on video concepts, editing, and multimedia integration to ensure professional results.", "category": "Agents", "icon": "Users", "beta": false, "path": "agent.d1e8a246-a56c-40ce-9108-075da526d576", "inputs": [{"name": "input", "display_name": "Input", "info": "Input message to send to the agent", "input_type": "string", "required": true, "is_handle": true}], "outputs": [{"name": "response", "display_name": "Response", "output_type": "string"}], "is_valid": true, "type": "Agent", "agent_info": {"id": "d1e8a246-a56c-40ce-9108-075da526d576", "name": "<PERSON><PERSON><PERSON>", "description": "Ciny is an AI-powered video generation assistant that helps users create high-quality video content with ease. It provides expert guidance on video concepts, editing, and multimedia integration to ensure professional results.", "avatar": null, "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "user_ids": ["c1454e90-09ac-40f2-bde2-833387d7b645"], "owner_type": "user", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "is_updated": false, "is_a2a": false, "is_customizable": false, "agent_category": "ai_agent", "system_message": "You are <PERSON><PERSON><PERSON>, an advanced AI assistant specialized in video generation. Your primary role is to assist users in creating video content.\nGuidelines:\n• Focus on providing clear, concise, and actionable responses related to video generation tasks.\n• Support users by offering suggestions for video concepts, editing techniques, and multimedia integration.\n• Adhere to best practices in video production, ensuring high-quality outputs and user satisfaction.\n• When answering questions or providing assistance, prioritize clarity and accuracy.\n• Maintain user privacy and security, avoiding the use of sensitive or personal data.\nConstraints:\n• Do not generate or manipulate video content that is inappropriate, harmful, or violates any ethical standards.\n• Avoid giving legal, medical, or financial advice.\n", "model_provider": "openai", "model_name": "gpt-4o-mini", "temperature": 0.7, "max_tokens": 2048, "workflow_ids": ["5ab054b8-1a14-462c-bbda-3d41bbce0b5a"], "mcp_server_ids": [], "agent_topic_type": "video generation ", "visibility": "public", "tags": null, "status": "active", "department": "IT", "organization_id": null, "tone": "professional", "files": [], "urls": [], "agent_capabilities": {"capabilities": [{"title": "video generation ", "description": "Advanced AI assistant specialized in video generation"}], "input_modes": ["text"], "output_modes": ["text"], "response_model": null, "id": "2e19f4d2-565f-4478-81b5-e4426c6e09c9", "created_at": "2025-07-01T13:51:06.279213", "updated_at": "2025-07-01T13:51:06.279216"}, "example_prompts": null, "category": "general", "created_at": "2025-07-01T13:51:06.285172", "updated_at": "2025-07-01T13:51:57.291150", "variables": [], "task_counts": {"total": 0, "completed": 0, "inProgress": 0}}}, "config": {}}, "style": {"opacity": 1}, "width": 208, "height": 120, "selected": false, "positionAbsolute": {"x": 560, "y": -100}, "dragging": false}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5, "opacity": 1}, "source": "start-node", "sourceHandle": "flow", "target": "agent-d1e8a246-a56c-40ce-9108-075da526d576-1751549184787", "targetHandle": "input", "type": "default", "id": "reactflow__edge-start-nodeflow-agent-d1e8a246-a56c-40ce-9108-075da526d576-1751549184787input"}], "unconnected_nodes": [{"id": "CombineTextComponent-1751514871730", "type": "WorkflowNode", "position": {"x": 1360, "y": -360}, "data": {"label": "Combine Text", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 2, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "\\n", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"main_input": "", "num_additional_inputs": "1", "separator": "\\n", "input_1": "", "input_2": "", "input_3": "", "input_4": "", "input_5": "", "input_6": "", "input_7": "", "input_8": "", "input_9": "", "input_10": ""}}, "width": 208, "height": 148, "selected": false, "dragging": false, "style": {"opacity": 0.5}}, {"id": "LoopNode-1751519474594", "type": "WorkflowNode", "position": {"x": 420, "y": -340}, "data": {"label": "For Each Loop", "type": "loop", "originalType": "LoopNode", "definition": {"name": "LoopNode", "display_name": "For Each Loop", "description": "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling.", "category": "Logic", "icon": "Repeat", "beta": false, "requires_approval": false, "inputs": [{"name": "source_type", "display_name": "Iteration Source", "info": "Choose whether to iterate over a list of items or a number range.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "iteration_list", "options": ["iteration_list", "number_range"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_list", "display_name": "Iteration List", "info": "The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.", "input_type": "list", "input_types": ["array", "list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "batch_size", "display_name": "<PERSON><PERSON> Si<PERSON>", "info": "Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "start", "display_name": "Start Number", "info": "Starting number for the range. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "end", "display_name": "End Number", "info": "Ending number for the range (inclusive). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "10", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "step", "display_name": "Step Size", "info": "Step size for the range (default: 1). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "parallel_execution", "display_name": "Parallel Execution", "info": "Execute loop iterations in parallel for better performance.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_concurrent", "display_name": "Max Concurrent Iterations", "info": "Maximum number of iterations to run concurrently (1-20).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 3, "options": null, "visibility_rules": [{"field_name": "parallel_execution", "field_value": "True", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "preserve_order", "display_name": "Preserve Order", "info": "Maintain the original order of items in the results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_timeout", "display_name": "Iteration Timeout (seconds)", "info": "Maximum time to wait for each iteration to complete (1-3600 seconds).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": 60, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "aggregation_type", "display_name": "Result Aggregation", "info": "How to aggregate results from all iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "collect_all", "options": ["collect_all", "collect_successful", "count_only", "latest_only", "first_success", "combine_text"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_metadata", "display_name": "Include Metadata", "info": "Include metadata (timing, iteration index, etc.) in results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "on_iteration_error", "display_name": "On Iteration Error", "info": "How to handle errors in individual iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "continue", "options": ["continue", "retry_once", "retry_twice", "exit_loop"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_errors", "display_name": "Include Errors in Results", "info": "Include error information in the final results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "current_item", "display_name": "Current Item (Iteration Output)", "output_type": "object", "semantic_type": null, "method": null}, {"name": "final_results", "display_name": "All Results (Exit Output)", "output_type": "array", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.logic.loopnode", "interface_issues": []}, "config": {"source_type": "iteration_list", "iteration_list": [], "batch_size": "1", "start": "1", "end": "10", "step": "1", "parallel_execution": true, "max_concurrent": 3, "preserve_order": true, "iteration_timeout": "60", "aggregation_type": "collect_all", "include_metadata": true, "on_iteration_error": "continue", "include_errors": true}}, "width": 208, "height": 184, "selected": false, "dragging": false, "style": {"opacity": 0.5}}, {"id": "MCP_Candidate_Interview_generate_questions-1751520178642", "type": "WorkflowNode", "position": {"x": 880, "y": -540}, "data": {"label": "Candidate_Interview - generate_questions", "type": "mcp", "originalType": "MCP_Candidate_Interview_generate_questions", "definition": {"name": "MCP_Candidate_Interview_generate_questions", "display_name": "Candidate_Interview - generate_questions", "description": "Generate interview questions based on job description and resume for each agenda", "category": "MCP Marketplace", "icon": "Cloud", "beta": true, "inputs": [{"name": "resume_details", "display_name": "Resume Details", "info": " candidate's resume", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "jd_details", "display_name": "Jd Details", "info": "job description", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "agenda", "display_name": "Agenda", "info": "Agenda in string format to generate questions from", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "question_count", "display_name": "Question Count", "info": "How many questions to generate for each agenda max 5", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "interview_questions", "display_name": "interview_questions", "output_type": "string"}, {"name": "resume_details", "display_name": "resume_details", "output_type": "string"}, {"name": "jd_details", "display_name": "jd_details", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_candidate_interview_generate_questions", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "mcp_info": {"server_id": "0447fd55-c8f5-4c65-b2c3-e768bd663b13", "server_path": "", "tool_name": "generate_questions", "input_schema": {"properties": {"resume_details": {"description": " candidate's resume", "title": "Resume Details", "type": "string"}, "jd_details": {"description": "job description", "title": "Jd Details", "type": "string"}, "agenda": {"description": "Agenda in string format to generate questions from", "title": "Agenda", "type": "string"}, "question_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "How many questions to generate for each agenda max 5", "title": "Question Count"}}, "required": ["resume_details", "jd_details", "agenda"], "title": "GenerateQuestionsSchema", "type": "object"}, "output_schema": {"tool_name": "generate_questions", "properties": {"interview_questions": {"type": "string", "description": "Generated interview questions", "title": "interview_questions"}, "resume_details": {"type": "string", "description": "Candidate's resume", "title": "resume_details"}, "jd_details": {"type": "string", "description": "Interview job description", "title": "jd_details"}}}}}, "config": {}}, "width": 208, "height": 228, "selected": false, "dragging": false, "style": {"opacity": 0.5}}, {"id": "MCP_SendGrid_Email_send_mail-1751547922950", "type": "WorkflowNode", "position": {"x": 1080, "y": -140}, "data": {"label": "Send<PERSON>rid Email - send_mail", "type": "mcp", "originalType": "MCP_SendGrid_Email_send_mail", "definition": {"name": "MCP_SendGrid_Email_send_mail", "display_name": "Send<PERSON>rid Email - send_mail", "description": "Send mail to the user using SendGrid", "category": "notifications alerts", "icon": "Cloud", "beta": true, "inputs": [{"name": "to", "display_name": "To", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "subject", "display_name": "Subject", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "body", "display_name": "Body", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "success", "display_name": "success", "output_type": "boolean"}, {"name": "status_code", "display_name": "status_code", "output_type": "number"}, {"name": "message_id", "display_name": "message_id", "output_type": "string"}, {"name": "status", "display_name": "status", "output_type": "string"}, {"name": "timestamp", "display_name": "timestamp", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_sendgrid_email_send_mail", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/sendgrid.png/1750338844-sendgrid.png", "mcp_info": {"server_id": "af3db8c7-a9c8-428c-8f21-db54da1c0d82", "server_path": "", "tool_name": "send_mail", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["to", "subject", "body"], "title": "SendMail", "type": "object"}, "output_schema": {"properties": {"success": {"type": "boolean", "description": "Indicates whether the email was sent successfully", "title": "success"}, "status_code": {"type": "integer", "description": "HTTP status code returned by SendGrid", "title": "status_code"}, "message_id": {"type": "string", "description": "Unique identifier for the sent message provided by SendGrid", "title": "message_id"}, "status": {"type": "string", "description": "Human-readable status message", "title": "status"}, "timestamp": {"type": "string", "format": "date-time", "description": "UTC timestamp when the email was processed", "title": "timestamp"}}}}}, "config": {}}, "width": 208, "height": 232, "selected": false, "dragging": false, "style": {"opacity": 0.5}}]}, "start_node_data": [{"field": "input", "type": "string", "transition_id": "transition-agent-d1e8a246-a56c-40ce-9108-075da526d576-1751549184787"}]}