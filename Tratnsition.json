{"nodes": [{"id": "agent-d1e8a246-a56c-40ce-9108-075da526d576", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "agent-d1e8a246-a56c-40ce-9108-075da526d576", "input_schema": {"predefined_fields": [{"field_name": "input", "data_type": {"type": "string", "description": "Input message to send to the agent"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "response", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}], "transitions": [{"id": "transition-agent-d1e8a246-a56c-40ce-9108-075da526d576-1751549184787", "sequence": 1, "transition_type": "initial", "execution_type": "agent", "node_info": {"node_id": "agent-d1e8a246-a56c-40ce-9108-075da526d576", "tools_to_use": [{"tool_id": 1, "tool_name": "agent-d1e8a246-a56c-40ce-9108-075da526d576", "tool_params": {"items": [{"field_name": "agent_type", "data_type": "string", "field_value": "component"}, {"field_name": "execution_type", "data_type": "string", "field_value": "response"}, {"field_name": "query", "data_type": "string", "field_value": ""}, {"field_name": "agent_config", "data_type": "object", "field_value": {}}]}}], "input_data": [], "output_data": []}, "result_resolution": {"node_type": "agent", "expected_result_structure": "dynamic", "handle_registry": {"input_handles": [{"handle_id": "input", "handle_name": "Input", "data_type": "string", "required": true, "description": "Input message to send to the agent"}], "output_handles": [{"handle_id": "response", "handle_name": "Response", "data_type": "string", "description": ""}]}, "result_path_hints": {"response": "response"}, "dynamic_discovery": {"enabled": true, "fallback_patterns": ["result.response", "output_data.response", "response.response", "data.response", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": false, "supports_nested_results": true, "requires_dynamic_discovery": true, "primary_output_handle": "response"}}, "approval_required": false, "end": true}]}