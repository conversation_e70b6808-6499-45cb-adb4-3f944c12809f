#!/usr/bin/env python3
"""
Test script to verify that system_message template variables are properly preprocessed
in the workflow schema converter.

This test verifies the fix for the issue where system_message template variables
were not being converted from {variable_name} to ${variable_name} format.
"""

import json
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    preprocess_template_variables,
    extract_template_variables_from_value
)


def test_system_message_template_variable_preprocessing():
    """Test that system_message template variables are properly preprocessed."""
    
    # Create a test workflow with an AgenticAI node containing template variables in system_message
    test_workflow = {
        "nodes": [
            {
                "id": "agent_node_1",
                "data": {
                    "type": "agent",
                    "originalType": "AgenticAI",
                    "definition": {
                        "name": "test_agent",
                        "display_name": "Test Agent",
                        "inputs": [
                            {"name": "query", "input_type": "string", "is_handle": True},
                            {"name": "system_message", "input_type": "string", "is_handle": False},
                            {"name": "input_variables", "input_type": "object", "is_handle": False}
                        ],
                        "outputs": [
                            {"name": "result", "output_type": "string", "is_handle": True}
                        ]
                    },
                    "config": {
                        "query": "Analyze the document",
                        "system_message": "You are an expert analyst. Please analyze the {document_type} provided by {user_name}.",
                        "input_variables": {
                            "context": "Additional context from {source_system}",
                            "priority": "high"
                        },
                        "execution_type": "response"
                    }
                }
            }
        ],
        "edges": [],
        "mcp_configs": []
    }
    
    print("🧪 Testing system_message template variable preprocessing...")
    print(f"Original system_message: {test_workflow['nodes'][0]['data']['config']['system_message']}")
    
    # Convert the workflow to transition schema
    try:
        transition_schema = convert_workflow_to_transition_schema(test_workflow)
        
        # Find the agent node in the transition_nodes (before transitions are created)
        agent_node = None
        for node in transition_schema.get("transition_nodes", []):
            if node.get("transition_id") == "agent_node_1":
                agent_node = node
                break

        if not agent_node:
            print("❌ ERROR: Agent node not found in converted schema")
            print(f"Available nodes: {[n.get('transition_id') for n in transition_schema.get('transition_nodes', [])]}")
            return False
        
        # Extract the system_message parameter from the agent tool
        tools_to_use = agent_node.get("tools_to_use", [])
        if not tools_to_use:
            print("❌ ERROR: No tools found in agent node")
            return False

        agent_tool = tools_to_use[0]
        tool_params = agent_tool.get("tool_params", {}).get("items", [])
        
        system_message_param = None
        for param in tool_params:
            if param.get("field_name") == "system_message":
                system_message_param = param
                break
        
        if not system_message_param:
            print("❌ ERROR: system_message parameter not found in tool params")
            return False
        
        processed_system_message = system_message_param.get("field_value", "")
        print(f"Processed system_message: {processed_system_message}")
        
        # Verify that template variables were converted from {variable} to ${variable}
        expected_system_message = "You are an expert analyst. Please analyze the ${document_type} provided by ${user_name}."
        
        if processed_system_message == expected_system_message:
            print("✅ SUCCESS: system_message template variables were properly preprocessed!")
            return True
        else:
            print(f"❌ ERROR: Expected: {expected_system_message}")
            print(f"❌ ERROR: Got: {processed_system_message}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: Exception during conversion: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_template_variable_extraction():
    """Test that template variables are properly extracted from system_message."""
    
    print("\n🧪 Testing template variable extraction from system_message...")
    
    # Test the preprocessing function directly
    original_message = "You are an expert {role}. Analyze {document_type} for {client_name}."
    processed_message = preprocess_template_variables(original_message, "system_message")
    
    print(f"Original: {original_message}")
    print(f"Processed: {processed_message}")
    
    expected_processed = "You are an expert ${role}. Analyze ${document_type} for ${client_name}."
    
    if processed_message == expected_processed:
        print("✅ SUCCESS: Template variable preprocessing works correctly!")
    else:
        print(f"❌ ERROR: Expected: {expected_processed}")
        print(f"❌ ERROR: Got: {processed_message}")
        return False
    
    # Test template variable extraction
    extracted_variables = extract_template_variables_from_value(processed_message)
    print(f"Extracted variables: {extracted_variables}")
    
    expected_variables = {"role", "document_type", "client_name"}
    extracted_variable_names = set(extracted_variables.keys())
    
    if extracted_variable_names == expected_variables:
        print("✅ SUCCESS: Template variable extraction works correctly!")
        return True
    else:
        print(f"❌ ERROR: Expected variables: {expected_variables}")
        print(f"❌ ERROR: Got variables: {extracted_variable_names}")
        return False


def main():
    """Run all tests."""
    print("=" * 80)
    print("🚀 TESTING SYSTEM_MESSAGE TEMPLATE VARIABLE PREPROCESSING FIX")
    print("=" * 80)
    
    test1_passed = test_template_variable_extraction()
    test2_passed = test_system_message_template_variable_preprocessing()
    
    print("\n" + "=" * 80)
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED! The system_message template variable fix is working correctly.")
        return 0
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
